function generateCircularTrajSensorData()
    % Generate the circular trajectory sensor data used in the "Pose Estimation
    % From Asynchronous Sensors" demo. 
    
    %   Copyright 2018 The MathWorks, Inc.    
    numRevs = 5;
    Fs = 100;   % Maximum MARG rate
    gpsFs = 5;  % Maximum GPS rate.
    ratio = Fs./gpsFs;
    refloc = [42.2825 -72.3430 53.0352];
    magField = [19.5281 -5.0741 48.0067];
    
    Fs = 100;
    trajData = circleTraj(Fs, numRevs);
    
    trajOrient = trajData.Orientation;
    trajVel = trajData.Velocity;
    trajPos = trajData.Position;
    trajAcc = trajData.Acceleration;
    trajAngVel = trajData.AngularVelocity;
    
    %% Setup the random number generator
    rng(1)
    
    %% IMU Simulation 
    imu = imuSensor('accel-gyro-mag', 'SampleRate', Fs);
    imu.MagneticField = magField;
    
    % Accelerometer
    imu.Accelerometer.MeasurementRange =  19.6133;
    imu.Accelerometer.Resolution = 0.001;%default= 0.0023928; %units: Meters per second squared (m/s²)
    imu.Accelerometer.NoiseDensity = 0.001 %default=0.0012356; units: Meters per second squared per square root Hertz (m/s²/√Hz)
    
    % Gyroscope
    imu.Gyroscope.MeasurementRange = deg2rad(250);
    imu.Gyroscope.Resolution = deg2rad(0.01);%default = 0.0625   % unit : (Radians per second (rad/s)) from 0.01 degrees/second.
    imu.Gyroscope.ConstantBias = deg2rad([1 1 3]);% default = deg2rad([3.125 3.125 7]);
    imu.Gyroscope.AxesMisalignment = [1.5 0 3];
    imu.Gyroscope.NoiseDensity = deg2rad(0.025);  % unit: Radians per second per square root Hertz (rad/s/√Hz)
    
    % Magnetometer
    imu.Magnetometer.MeasurementRange = 1000;
    imu.Magnetometer.Resolution = 0.1;
    imu.Magnetometer.NoiseDensity = 0.1/ sqrt(50);
    
    [accel, gyro, mag] = imu(trajAcc, trajAngVel, trajOrient);
    
    %% GPS Simulation
    gps = gpsSensor('UpdateRate', gpsFs);
    gps.ReferenceLocation = refloc;     
    gps.DecayFactor = 0.5;              
    gps.HorizontalPositionAccuracy = 0.02 % default = 0.5;   
    gps.VerticalPositionAccuracy =  0.10; %1.6;
    gps.VelocityAccuracy = 0.01; % (for RTK scenario) % default = 0.1;           
    
    [llaGPSRate, gpsvelGPSRate] = gps( trajPos(1:ratio:end,:), ...
        trajVel(1:ratio:end, :)); %#ok<BDSCI>
    
    lla = upsampleGPS(llaGPSRate, ratio);
    gpsvel = upsampleGPS(gpsvelGPSRate, ratio);
    
    save('CircularTrajectorySensorData.mat', 'Fs', 'gpsFs', 'refloc', ...
        'magField', 'trajData', 'accel', 'gyro', 'mag', 'lla', 'gpsvel');

end

function trajData = circleTraj(fs, numRevs)
    % Generate a circular motion trajectory
    
    % N = 10000; 
    r = 20%8.42; % (m)
    speed = 2.50; % (m/s)
    center = [0, 0]; % (m)
    initialYaw = 90; % (degrees)

    

    

    % --- Vertical Sinusoidal Motion ---
    vertical_amplitude = 5; % (m) - Amplitude of vertical motion
    vertical_frequency = 0.1; % (Hz) - Frequency of vertical motion
    % ---------------------------------
    
    % Define angles theta and corresponding times of arrival t.
    revTime = 2*pi*r / speed;
    theta = (0:pi/2:2*pi* numRevs).';
    t = linspace(0, revTime*numRevs, numel(theta)).';
    totalTime = revTime * numRevs;
    N = floor(totalTime * fs);  % Calculate N based on total time and fs
    % pos = zeros(N,3);
    
    % Define position.
    x = r .* cos(theta) + center(1);
    y = r .* sin(theta) + center(2);
    z = zeros(size(x));
    % z = vertical_amplitude * sin(2*pi*vertical_frequency*t); % Vertical position


    % --- Periodic Speed Change Parameters ---
    % speed_change_interval = 5; % (seconds) - Time between speed changes
    % speed_change_magnitude = 2.0; % (m/s) - Magnitude of speed change
    % --------------------------------------
    % --- Apply Periodic Speed Changes ---
    % for ii = 1:min(N, length(t))
    %     time_since_start = t(ii);
    %     if mod(floor(time_since_start / speed_change_interval), 2) == 0
    %         speed = 2.50 + speed_change_magnitude; % Increase speed
    %     else
    %         speed = 2.50 - speed_change_magnitude; % Decrease speed
    %     end
    %     revTime = 2*pi*r / speed; % Recalculate revTime
    %     % Recalculate theta from current time onwards
    %     if ii > 1
    %         theta(ii:end) = theta(ii-1) + (t(ii:end) - t(ii-1)) * speed / r;
    %     end
    %     x = r .* cos(theta) + center(1);
    %     y = r .* sin(theta) + center(2);
    % end
    % ----------------------------------

    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

    % % --- Periodic Jump Parameters ---
    jump_interval = 5; % (seconds) - Time between jumps
    jump_magnitude = 7.5; % (m) - Magnitude of the jump
    % % -------------------------------
    
    % % --- Apply Periodic Jumps ---
    for ii = 1:min(N, length(t))
        time_since_start = t(ii);
        if mod(floor(time_since_start / jump_interval), 2) == 0
            x(ii) = x(ii) + jump_magnitude; % +5m jump
            y(ii) = y(ii) + jump_magnitude;
        else
            x(ii) = x(ii) - jump_magnitude; % -5m jump
            y(ii) = y(ii) - jump_magnitude;
        end
    end
    % % ----------------------------
    

    position = [x, y, z];


    yaw = zeros(size(theta)) + deg2rad(initialYaw);  % Corrected yaw
    % yaw = theta + deg2rad(initialYaw);
    yaw = mod(yaw, 2*pi);


        
    % --- Custom Orientation ---
    roll_amplitude = deg2rad(89); % 10 degrees roll
    roll_frequency = 0.2; % 0.2 Hz roll frequency
    pitch_amplitude = deg2rad(89); % 5 degrees pitch
    pitch_frequency = 1; % 0.1 Hz pitch frequency

    % roll = roll_amplitude * sin(2*pi*roll_frequency*t);
    pitch = pitch_amplitude * cos(2*pi*pitch_frequency*t); 
    % --------------------------
    % pitch = zeros(size(yaw));
    roll = zeros(size(yaw));

    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    % --- Periodic Wave Motion Parameters ---
    roll_wave_amplitude = deg2rad(89); % 15 degrees roll amplitude
    roll_wave_frequency = 0.15; % 0.15 Hz roll frequency
    pitch_wave_amplitude = deg2rad(8); % 8 degrees pitch amplitude
    pitch_wave_frequency = 0.12; % 0.12 Hz pitch frequency (slightly different from roll)
    
    % --- Periodic Roll/Pitch Jump Parameters ---
    roll_jump_interval = 10; % (seconds) - Time between roll jumps
    roll_jump_magnitude = deg2rad(20); % (degrees) - Magnitude of roll jump
    pitch_jump_interval = 5; % (seconds) - Time between pitch jumps
    pitch_jump_magnitude = deg2rad(10); % (degrees) - Magnitude of pitch jump
    % ----------------------------------------
    
    % --- Apply Wave Motion to Roll and Pitch ---
    % roll = roll_wave_amplitude * sin(2*pi*roll_wave_frequency*t);
    % pitch = pitch_wave_amplitude * cos(2*pi*pitch_wave_frequency*t);
    
    % --- Apply Periodic Roll/Pitch Jumps ---
    % for ii = 1:length(t)
    %     time_since_start = t(ii);
    % 
    %     % Roll jumps
    %     if mod(floor(time_since_start / roll_jump_interval), 2) == 0
    %         roll(ii) = roll(ii) + roll_jump_magnitude;
    %     else
    %         roll(ii) = roll(ii) - roll_jump_magnitude;
    %     end
    % 
    %     % Pitch jumps
    %     if mod(floor(time_since_start / pitch_jump_interval), 2) == 0
    %         pitch(ii) = pitch(ii) + pitch_jump_magnitude;
    %     else
    %         pitch(ii) = pitch(ii) - pitch_jump_magnitude;
    %     end
    % end
    % -----------------------------------------
    % 
    % roll = roll_wave_amplitude * sin(2*pi*roll_wave_frequency*t);
    % pitch = pitch_wave_amplitude * cos(2*pi*pitch_wave_frequency*t);
    % disp(["Time: ", num2str(t(1)), ", Roll: ", num2str(rad2deg(roll(1))), ", Pitch: ", num2str(rad2deg(pitch(1)))]); % Print at the start
    % disp(["Time: ", num2str(t(round(end/2))), ", Roll: ", num2str(rad2deg(roll(round(end/2)))), ", Pitch: ", num2str(rad2deg(pitch(round(end/2))))]); % Print in the middle
    % disp(["Time: ", num2str(t(end)), ", Roll: ", num2str(rad2deg(roll(end))), ", Pitch: ", num2str(rad2deg(pitch(end)))]); % Print at the end
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

  


    

    

    
    orientation = quaternion([yaw, pitch, roll], 'euler', ...
        'ZYX', 'frame');
    
    % Generate trajectory.
    groundTruth = waypointTrajectory('SampleRate', fs, ...
        'Waypoints', position, ...
        'TimeOfArrival', t, ...
        'Orientation', orientation);
    
    
    pos = zeros(N,3);
    q = quaternion.zeros(N,1);
    vel = zeros(N,3);
    acc = zeros(N,3);
    av = zeros(N,3);
    
    
    for ii=1:N
        [pos(ii,:), qtmp, vel(ii,:), acc(ii,:), av(ii,:)] = groundTruth();
        q(ii) = qtmp;
    end
    
    
    trajData.Orientation = q;
    trajData.Velocity = vel;
    trajData.Position = pos;
    trajData.Acceleration = acc;
    trajData.AngularVelocity = av;
end

function y = upsampleGPS(x, ratio)
    %UPSAMPLEGPS Sample and hold GPS input to higher rate
    xup = repmat(x, 1,1,ratio);
    xp = permute(xup,[3 1 2]);
    y = reshape(xp, [], 3);
end


    